#ifndef __gl3w_h_
#define __gl3w_h_

#include <GL/glcorearb.h>

#ifndef __gl_h_
#define __gl_h_
#endif

#ifdef __cplusplus
extern "C" {
#endif

/* gl3w api */
int gl3wInit(void);
int gl3wIsSupported(int major, int minor);
void *gl3wGetProcAddress(const char *proc);

/* OpenGL functions */
extern PFNGLCULLFACEPROC gl3wCullFace;
extern PFNGLFRONTFACEPROC gl3wFrontFace;
extern PFNGLHINTPROC gl3wHint;
extern PFNGLLINEWIDTHPROC gl3wLineWidth;
extern PFNGLPOINTSIZEPROC gl3wPointSize;
extern PFNGLPOLYGONMODEPROC gl3wPolygonMode;
extern PFNGLSCISSORPROC gl3wScissor;
extern PFNGLTEXPARAMETERFPROC gl3wTexParameterf;
extern PFNGLTEXPARAMETERFVPROC gl3wTexParameterfv;
extern PFNGLTEXPARAMETERIPROC gl3wTexParameteri;
extern PFNGLTEXPARAMETERIVPROC gl3wTexParameteriv;
extern PFNGLTEXIMAGE1DPROC gl3wTexImage1D;
extern PFNGLTEXIMAGE2DPROC gl3wTexImage2D;
extern PFNGLDRAWBUFFERPROC gl3wDrawBuffer;
extern PFNGLCLEARPROC gl3wClear;
extern PFNGLCLEARCOLORPROC gl3wClearColor;
extern PFNGLCLEARSTENCILPROC gl3wClearStencil;
extern PFNGLCLEARDEPTHPROC gl3wClearDepth;
extern PFNGLSTENCILMASKPROC gl3wStencilMask;
extern PFNGLCOLORMASKPROC gl3wColorMask;
extern PFNGLDEPTHMASKPROC gl3wDepthMask;
extern PFNGLDISABLEPROC gl3wDisable;
extern PFNGLENABLEPROC gl3wEnable;
extern PFNGLFINISHPROC gl3wFinish;
extern PFNGLFLUSHPROC gl3wFlush;
extern PFNGLBLENDFUNCPROC gl3wBlendFunc;
extern PFNGLLOGICOPPROC gl3wLogicOp;
extern PFNGLSTENCILFUNCPROC gl3wStencilFunc;
extern PFNGLSTENCILOPPROC gl3wStencilOp;
extern PFNGLDEPTHFUNCPROC gl3wDepthFunc;
extern PFNGLPIXELSTOREFPROC gl3wPixelStoref;
extern PFNGLPIXELSTOREIPROC gl3wPixelStorei;
extern PFNGLREADBUFFERPROC gl3wReadBuffer;
extern PFNGLREADPIXELSPROC gl3wReadPixels;
extern PFNGLGETBOOLEANVPROC gl3wGetBooleanv;
extern PFNGLGETDOUBLEVPROC gl3wGetDoublev;
extern PFNGLGETERRORPROC gl3wGetError;
extern PFNGLGETFLOATVPROC gl3wGetFloatv;
extern PFNGLGETINTEGERVPROC gl3wGetIntegerv;
extern PFNGLGETSTRINGPROC gl3wGetString;
extern PFNGLGETTEXIMAGEPROC gl3wGetTexImage;
extern PFNGLGETTEXPARAMETERFVPROC gl3wGetTexParameterfv;
extern PFNGLGETTEXPARAMETERIVPROC gl3wGetTexParameteriv;
extern PFNGLGETTEXLEVELPARAMETERFVPROC gl3wGetTexLevelParameterfv;
extern PFNGLGETTEXLEVELPARAMETERIVPROC gl3wGetTexLevelParameteriv;
extern PFNGLISENABLEDPROC gl3wIsEnabled;
extern PFNGLDEPTHRANGEPROC gl3wDepthRange;
extern PFNGLVIEWPORTPROC gl3wViewport;
extern PFNGLDRAWARRAYSPROC gl3wDrawArrays;
extern PFNGLDRAWELEMENTSPROC gl3wDrawElements;
extern PFNGLGETPOINTERVPROC gl3wGetPointerv;
extern PFNGLPOLYGONOFFSETPROC gl3wPolygonOffset;
extern PFNGLCOPYTEXIMAGE1DPROC gl3wCopyTexImage1D;
extern PFNGLCOPYTEXIMAGE2DPROC gl3wCopyTexImage2D;
extern PFNGLCOPYTEXSUBIMAGE1DPROC gl3wCopyTexSubImage1D;
extern PFNGLCOPYTEXSUBIMAGE2DPROC gl3wCopyTexSubImage2D;
extern PFNGLTEXSUBIMAGE1DPROC gl3wTexSubImage1D;
extern PFNGLTEXSUBIMAGE2DPROC gl3wTexSubImage2D;
extern PFNGLBINDTEXTUREPROC gl3wBindTexture;
extern PFNGLDELETETEXTURESPROC gl3wDeleteTextures;
extern PFNGLGENTEXTURESPROC gl3wGenTextures;
extern PFNGLISTEXTUREPROC gl3wIsTexture;
extern PFNGLBLENDCOLORPROC gl3wBlendColor;
extern PFNGLBLENDEQUATIONPROC gl3wBlendEquation;
extern PFNGLDRAWRANGEELEMENTSPROC gl3wDrawRangeElements;
extern PFNGLTEXIMAGE3DPROC gl3wTexImage3D;
extern PFNGLTEXSUBIMAGE3DPROC gl3wTexSubImage3D;
extern PFNGLCOPYTEXSUBIMAGE3DPROC gl3wCopyTexSubImage3D;
extern PFNGLACTIVETEXTUREPROC gl3wActiveTexture;
extern PFNGLSAMPLECOVERAGEPROC gl3wSampleCoverage;
extern PFNGLCOMPRESSEDTEXIMAGE3DPROC gl3wCompressedTexImage3D;
extern PFNGLCOMPRESSEDTEXIMAGE2DPROC gl3wCompressedTexImage2D;
extern PFNGLCOMPRESSEDTEXIMAGE1DPROC gl3wCompressedTexImage1D;
extern PFNGLCOMPRESSEDTEXSUBIMAGE3DPROC gl3wCompressedTexSubImage3D;
extern PFNGLCOMPRESSEDTEXSUBIMAGE2DPROC gl3wCompressedTexSubImage2D;
extern PFNGLCOMPRESSEDTEXSUBIMAGE1DPROC gl3wCompressedTexSubImage1D;
extern PFNGLGETCOMPRESSEDTEXIMAGEPROC gl3wGetCompressedTexImage;
extern PFNGLBLENDFUNCSEPARATEPROC gl3wBlendFuncSeparate;
extern PFNGLMULTIDRAWARRAYSPROC gl3wMultiDrawArrays;
extern PFNGLMULTIDRAWELEMENTSPROC gl3wMultiDrawElements;
extern PFNGLPOINTPARAMETERFPROC gl3wPointParameterf;
extern PFNGLPOINTPARAMETERFVPROC gl3wPointParameterfv;
extern PFNGLPOINTPARAMETERIPROC gl3wPointParameteri;
extern PFNGLPOINTPARAMETERIVPROC gl3wPointParameteriv;
extern PFNGLGENQUERIESPROC gl3wGenQueries;
extern PFNGLDELETEQUERIESPROC gl3wDeleteQueries;
extern PFNGLISQUERYPROC gl3wIsQuery;
extern PFNGLBEGINQUERYPROC gl3wBeginQuery;
extern PFNGLENDQUERYPROC gl3wEndQuery;
extern PFNGLGETQUERYIVPROC gl3wGetQueryiv;
extern PFNGLGETQUERYOBJECTIVPROC gl3wGetQueryObjectiv;
extern PFNGLGETQUERYOBJECTUIVPROC gl3wGetQueryObjectuiv;
extern PFNGLBINDBUFFERPROC gl3wBindBuffer;
extern PFNGLDELETEBUFFERSPROC gl3wDeleteBuffers;
extern PFNGLGENBUFFERSPROC gl3wGenBuffers;
extern PFNGLISBUFFERPROC gl3wIsBuffer;
extern PFNGLBUFFERDATAPROC gl3wBufferData;
extern PFNGLBUFFERSUBDATAPROC gl3wBufferSubData;
extern PFNGLGETBUFFERSUBDATAPROC gl3wGetBufferSubData;
extern PFNGLMAPBUFFERPROC gl3wMapBuffer;
extern PFNGLUNMAPBUFFERPROC gl3wUnmapBuffer;
extern PFNGLGETBUFFERPARAMETERIVPROC gl3wGetBufferParameteriv;
extern PFNGLGETBUFFERPOINTERVPROC gl3wGetBufferPointerv;
extern PFNGLBLENDEQUATIONSEPARATEPROC gl3wBlendEquationSeparate;
extern PFNGLDRAWBUFFERSPROC gl3wDrawBuffers;
extern PFNGLSTENCILOPSEPARATEPROC gl3wStencilOpSeparate;
extern PFNGLSTENCILFUNCSEPARATEPROC gl3wStencilFuncSeparate;
extern PFNGLSTENCILMASKSEPARATEPROC gl3wStencilMaskSeparate;
extern PFNGLATTACHSHADERPROC gl3wAttachShader;
extern PFNGLBINDATTRIBLOCATIONPROC gl3wBindAttribLocation;
extern PFNGLCOMPILESHADERPROC gl3wCompileShader;
extern PFNGLCREATEPROGRAMPROC gl3wCreateProgram;
extern PFNGLCREATESHADERPROC gl3wCreateShader;
extern PFNGLDELETEPROGRAMPROC gl3wDeleteProgram;
extern PFNGLDELETESHADERPROC gl3wDeleteShader;
extern PFNGLDETACHSHADERPROC gl3wDetachShader;
extern PFNGLDISABLEVERTEXATTRIBARRAYPROC gl3wDisableVertexAttribArray;
extern PFNGLENABLEVERTEXATTRIBARRAYPROC gl3wEnableVertexAttribArray;
extern PFNGLGETACTIVEATTRIBPROC gl3wGetActiveAttrib;
extern PFNGLGETACTIVEUNIFORMPROC gl3wGetActiveUniform;
extern PFNGLGETATTACHEDSHADERSPROC gl3wGetAttachedShaders;
extern PFNGLGETATTRIBLOCATIONPROC gl3wGetAttribLocation;
extern PFNGLGETPROGRAMIVPROC gl3wGetProgramiv;
extern PFNGLGETPROGRAMINFOLOGPROC gl3wGetProgramInfoLog;
extern PFNGLGETSHADERIVPROC gl3wGetShaderiv;
extern PFNGLGETSHADERINFOLOGPROC gl3wGetShaderInfoLog;
extern PFNGLGETSHADERSOURCEPROC gl3wGetShaderSource;
extern PFNGLGETUNIFORMLOCATIONPROC gl3wGetUniformLocation;
extern PFNGLGETUNIFORMFVPROC gl3wGetUniformfv;
extern PFNGLGETUNIFORMIVPROC gl3wGetUniformiv;
extern PFNGLGETVERTEXATTRIBDVPROC gl3wGetVertexAttribdv;
extern PFNGLGETVERTEXATTRIBFVPROC gl3wGetVertexAttribfv;
extern PFNGLGETVERTEXATTRIBIVPROC gl3wGetVertexAttribiv;
extern PFNGLGETVERTEXATTRIBPOINTERVPROC gl3wGetVertexAttribPointerv;
extern PFNGLISPROGRAMPROC gl3wIsProgram;
extern PFNGLISSHADERPROC gl3wIsShader;
extern PFNGLLINKPROGRAMPROC gl3wLinkProgram;
extern PFNGLSHADERSOURCEPROC gl3wShaderSource;
extern PFNGLUSEPROGRAMPROC gl3wUseProgram;
extern PFNGLUNIFORM1FPROC gl3wUniform1f;
extern PFNGLUNIFORM2FPROC gl3wUniform2f;
extern PFNGLUNIFORM3FPROC gl3wUniform3f;
extern PFNGLUNIFORM4FPROC gl3wUniform4f;
extern PFNGLUNIFORM1IPROC gl3wUniform1i;
extern PFNGLUNIFORM2IPROC gl3wUniform2i;
extern PFNGLUNIFORM3IPROC gl3wUniform3i;
extern PFNGLUNIFORM4IPROC gl3wUniform4i;
extern PFNGLUNIFORM1FVPROC gl3wUniform1fv;
extern PFNGLUNIFORM2FVPROC gl3wUniform2fv;
extern PFNGLUNIFORM3FVPROC gl3wUniform3fv;
extern PFNGLUNIFORM4FVPROC gl3wUniform4fv;
extern PFNGLUNIFORM1IVPROC gl3wUniform1iv;
extern PFNGLUNIFORM2IVPROC gl3wUniform2iv;
extern PFNGLUNIFORM3IVPROC gl3wUniform3iv;
extern PFNGLUNIFORM4IVPROC gl3wUniform4iv;
extern PFNGLUNIFORMMATRIX2FVPROC gl3wUniformMatrix2fv;
extern PFNGLUNIFORMMATRIX3FVPROC gl3wUniformMatrix3fv;
extern PFNGLUNIFORMMATRIX4FVPROC gl3wUniformMatrix4fv;
extern PFNGLVALIDATEPROGRAMPROC gl3wValidateProgram;
extern PFNGLVERTEXATTRIB1DPROC gl3wVertexAttrib1d;
extern PFNGLVERTEXATTRIB1DVPROC gl3wVertexAttrib1dv;
extern PFNGLVERTEXATTRIB1FPROC gl3wVertexAttrib1f;
extern PFNGLVERTEXATTRIB1FVPROC gl3wVertexAttrib1fv;
extern PFNGLVERTEXATTRIB1SPROC gl3wVertexAttrib1s;
extern PFNGLVERTEXATTRIB1SVPROC gl3wVertexAttrib1sv;
extern PFNGLVERTEXATTRIB2DPROC gl3wVertexAttrib2d;
extern PFNGLVERTEXATTRIB2DVPROC gl3wVertexAttrib2dv;
extern PFNGLVERTEXATTRIB2FPROC gl3wVertexAttrib2f;
extern PFNGLVERTEXATTRIB2FVPROC gl3wVertexAttrib2fv;
extern PFNGLVERTEXATTRIB2SPROC gl3wVertexAttrib2s;
extern PFNGLVERTEXATTRIB2SVPROC gl3wVertexAttrib2sv;
extern PFNGLVERTEXATTRIB3DPROC gl3wVertexAttrib3d;
extern PFNGLVERTEXATTRIB3DVPROC gl3wVertexAttrib3dv;
extern PFNGLVERTEXATTRIB3FPROC gl3wVertexAttrib3f;
extern PFNGLVERTEXATTRIB3FVPROC gl3wVertexAttrib3fv;
extern PFNGLVERTEXATTRIB3SPROC gl3wVertexAttrib3s;
extern PFNGLVERTEXATTRIB3SVPROC gl3wVertexAttrib3sv;
extern PFNGLVERTEXATTRIB4NBVPROC gl3wVertexAttrib4Nbv;
extern PFNGLVERTEXATTRIB4NIVPROC gl3wVertexAttrib4Niv;
extern PFNGLVERTEXATTRIB4NSVPROC gl3wVertexAttrib4Nsv;
extern PFNGLVERTEXATTRIB4NUBPROC gl3wVertexAttrib4Nub;
extern PFNGLVERTEXATTRIB4NUBVPROC gl3wVertexAttrib4Nubv;
extern PFNGLVERTEXATTRIB4NUIVPROC gl3wVertexAttrib4Nuiv;
extern PFNGLVERTEXATTRIB4NUSVPROC gl3wVertexAttrib4Nusv;
extern PFNGLVERTEXATTRIB4BVPROC gl3wVertexAttrib4bv;
extern PFNGLVERTEXATTRIB4DPROC gl3wVertexAttrib4d;
extern PFNGLVERTEXATTRIB4DVPROC gl3wVertexAttrib4dv;
extern PFNGLVERTEXATTRIB4FPROC gl3wVertexAttrib4f;
extern PFNGLVERTEXATTRIB4FVPROC gl3wVertexAttrib4fv;
extern PFNGLVERTEXATTRIB4IVPROC gl3wVertexAttrib4iv;
extern PFNGLVERTEXATTRIB4SPROC gl3wVertexAttrib4s;
extern PFNGLVERTEXATTRIB4SVPROC gl3wVertexAttrib4sv;
extern PFNGLVERTEXATTRIB4UBVPROC gl3wVertexAttrib4ubv;
extern PFNGLVERTEXATTRIB4UIVPROC gl3wVertexAttrib4uiv;
extern PFNGLVERTEXATTRIB4USVPROC gl3wVertexAttrib4usv;
extern PFNGLVERTEXATTRIBPOINTERPROC gl3wVertexAttribPointer;
extern PFNGLUNIFORMMATRIX2X3FVPROC gl3wUniformMatrix2x3fv;
extern PFNGLUNIFORMMATRIX3X2FVPROC gl3wUniformMatrix3x2fv;
extern PFNGLUNIFORMMATRIX2X4FVPROC gl3wUniformMatrix2x4fv;
extern PFNGLUNIFORMMATRIX4X2FVPROC gl3wUniformMatrix4x2fv;
extern PFNGLUNIFORMMATRIX3X4FVPROC gl3wUniformMatrix3x4fv;
extern PFNGLUNIFORMMATRIX4X3FVPROC gl3wUniformMatrix4x3fv;
extern PFNGLCOLORMASKIPROC gl3wColorMaski;
extern PFNGLGETBOOLEANI_VPROC gl3wGetBooleani_v;
extern PFNGLGETINTEGERI_VPROC gl3wGetIntegeri_v;
extern PFNGLENABLEIPROC gl3wEnablei;
extern PFNGLDISABLEIPROC gl3wDisablei;
extern PFNGLISENABLEDIPROC gl3wIsEnabledi;
extern PFNGLBEGINTRANSFORMFEEDBACKPROC gl3wBeginTransformFeedback;
extern PFNGLENDTRANSFORMFEEDBACKPROC gl3wEndTransformFeedback;
extern PFNGLBINDBUFFERRANGEPROC gl3wBindBufferRange;
extern PFNGLBINDBUFFERBASEPROC gl3wBindBufferBase;
extern PFNGLTRANSFORMFEEDBACKVARYINGSPROC gl3wTransformFeedbackVaryings;
extern PFNGLGETTRANSFORMFEEDBACKVARYINGPROC gl3wGetTransformFeedbackVarying;
extern PFNGLCLAMPCOLORPROC gl3wClampColor;
extern PFNGLBEGINCONDITIONALRENDERPROC gl3wBeginConditionalRender;
extern PFNGLENDCONDITIONALRENDERPROC gl3wEndConditionalRender;
extern PFNGLVERTEXATTRIBIPOINTERPROC gl3wVertexAttribIPointer;
extern PFNGLGETVERTEXATTRIBIIVPROC gl3wGetVertexAttribIiv;
extern PFNGLGETVERTEXATTRIBIUIVPROC gl3wGetVertexAttribIuiv;
extern PFNGLVERTEXATTRIBI1IPROC gl3wVertexAttribI1i;
extern PFNGLVERTEXATTRIBI2IPROC gl3wVertexAttribI2i;
extern PFNGLVERTEXATTRIBI3IPROC gl3wVertexAttribI3i;
extern PFNGLVERTEXATTRIBI4IPROC gl3wVertexAttribI4i;
extern PFNGLVERTEXATTRIBI1UIPROC gl3wVertexAttribI1ui;
extern PFNGLVERTEXATTRIBI2UIPROC gl3wVertexAttribI2ui;
extern PFNGLVERTEXATTRIBI3UIPROC gl3wVertexAttribI3ui;
extern PFNGLVERTEXATTRIBI4UIPROC gl3wVertexAttribI4ui;
extern PFNGLVERTEXATTRIBI1IVPROC gl3wVertexAttribI1iv;
extern PFNGLVERTEXATTRIBI2IVPROC gl3wVertexAttribI2iv;
extern PFNGLVERTEXATTRIBI3IVPROC gl3wVertexAttribI3iv;
extern PFNGLVERTEXATTRIBI4IVPROC gl3wVertexAttribI4iv;
extern PFNGLVERTEXATTRIBI1UIVPROC gl3wVertexAttribI1uiv;
extern PFNGLVERTEXATTRIBI2UIVPROC gl3wVertexAttribI2uiv;
extern PFNGLVERTEXATTRIBI3UIVPROC gl3wVertexAttribI3uiv;
extern PFNGLVERTEXATTRIBI4UIVPROC gl3wVertexAttribI4uiv;
extern PFNGLVERTEXATTRIBI4BVPROC gl3wVertexAttribI4bv;
extern PFNGLVERTEXATTRIBI4SVPROC gl3wVertexAttribI4sv;
extern PFNGLVERTEXATTRIBI4UBVPROC gl3wVertexAttribI4ubv;
extern PFNGLVERTEXATTRIBI4USVPROC gl3wVertexAttribI4usv;
extern PFNGLGETUNIFORMUIVPROC gl3wGetUniformuiv;
extern PFNGLBINDFRAGDATALOCATIONPROC gl3wBindFragDataLocation;
extern PFNGLGETFRAGDATALOCATIONPROC gl3wGetFragDataLocation;
extern PFNGLUNIFORM1UIPROC gl3wUniform1ui;
extern PFNGLUNIFORM2UIPROC gl3wUniform2ui;
extern PFNGLUNIFORM3UIPROC gl3wUniform3ui;
extern PFNGLUNIFORM4UIPROC gl3wUniform4ui;
extern PFNGLUNIFORM1UIVPROC gl3wUniform1uiv;
extern PFNGLUNIFORM2UIVPROC gl3wUniform2uiv;
extern PFNGLUNIFORM3UIVPROC gl3wUniform3uiv;
extern PFNGLUNIFORM4UIVPROC gl3wUniform4uiv;
extern PFNGLTEXPARAMETERIIVPROC gl3wTexParameterIiv;
extern PFNGLTEXPARAMETERIUIVPROC gl3wTexParameterIuiv;
extern PFNGLGETTEXPARAMETERIIVPROC gl3wGetTexParameterIiv;
extern PFNGLGETTEXPARAMETERIUIVPROC gl3wGetTexParameterIuiv;
extern PFNGLCLEARBUFFERIVPROC gl3wClearBufferiv;
extern PFNGLCLEARBUFFERUIVPROC gl3wClearBufferuiv;
extern PFNGLCLEARBUFFERFVPROC gl3wClearBufferfv;
extern PFNGLCLEARBUFFERFIPROC gl3wClearBufferfi;
extern PFNGLGETSTRINGIPROC gl3wGetStringi;
extern PFNGLDRAWARRAYSINSTANCEDPROC gl3wDrawArraysInstanced;
extern PFNGLDRAWELEMENTSINSTANCEDPROC gl3wDrawElementsInstanced;
extern PFNGLTEXBUFFERPROC gl3wTexBuffer;
extern PFNGLPRIMITIVERESTARTINDEXPROC gl3wPrimitiveRestartIndex;
extern PFNGLGETINTEGER64I_VPROC gl3wGetInteger64i_v;
extern PFNGLGETBUFFERPARAMETERI64VPROC gl3wGetBufferParameteri64v;
extern PFNGLFRAMEBUFFERTEXTUREPROC gl3wFramebufferTexture;
extern PFNGLVERTEXATTRIBDIVISORPROC gl3wVertexAttribDivisor;
extern PFNGLMINSAMPLESHADINGPROC gl3wMinSampleShading;
extern PFNGLBLENDEQUATIONIPROC gl3wBlendEquationi;
extern PFNGLBLENDEQUATIONSEPARATEIPROC gl3wBlendEquationSeparatei;
extern PFNGLBLENDFUNCIPROC gl3wBlendFunci;
extern PFNGLBLENDFUNCSEPARATEIPROC gl3wBlendFuncSeparatei;
extern PFNGLISRENDERBUFFERPROC gl3wIsRenderbuffer;
extern PFNGLBINDRENDERBUFFERPROC gl3wBindRenderbuffer;
extern PFNGLDELETERENDERBUFFERSPROC gl3wDeleteRenderbuffers;
extern PFNGLGENRENDERBUFFERSPROC gl3wGenRenderbuffers;
extern PFNGLRENDERBUFFERSTORAGEPROC gl3wRenderbufferStorage;
extern PFNGLGETRENDERBUFFERPARAMETERIVPROC gl3wGetRenderbufferParameteriv;
extern PFNGLISFRAMEBUFFERPROC gl3wIsFramebuffer;
extern PFNGLBINDFRAMEBUFFERPROC gl3wBindFramebuffer;
extern PFNGLDELETEFRAMEBUFFERSPROC gl3wDeleteFramebuffers;
extern PFNGLGENFRAMEBUFFERSPROC gl3wGenFramebuffers;
extern PFNGLCHECKFRAMEBUFFERSTATUSPROC gl3wCheckFramebufferStatus;
extern PFNGLFRAMEBUFFERTEXTURE1DPROC gl3wFramebufferTexture1D;
extern PFNGLFRAMEBUFFERTEXTURE2DPROC gl3wFramebufferTexture2D;
extern PFNGLFRAMEBUFFERTEXTURE3DPROC gl3wFramebufferTexture3D;
extern PFNGLFRAMEBUFFERRENDERBUFFERPROC gl3wFramebufferRenderbuffer;
extern PFNGLGETFRAMEBUFFERATTACHMENTPARAMETERIVPROC gl3wGetFramebufferAttachmentParameteriv;
extern PFNGLGENERATEMIPMAPPROC gl3wGenerateMipmap;
extern PFNGLBLITFRAMEBUFFERPROC gl3wBlitFramebuffer;
extern PFNGLRENDERBUFFERSTORAGEMULTISAMPLEPROC gl3wRenderbufferStorageMultisample;
extern PFNGLFRAMEBUFFERTEXTURELAYERPROC gl3wFramebufferTextureLayer;
extern PFNGLMAPBUFFERRANGEPROC gl3wMapBufferRange;
extern PFNGLFLUSHMAPPEDBUFFERRANGEPROC gl3wFlushMappedBufferRange;
extern PFNGLBINDVERTEXARRAYPROC gl3wBindVertexArray;
extern PFNGLDELETEVERTEXARRAYSPROC gl3wDeleteVertexArrays;
extern PFNGLGENVERTEXARRAYSPROC gl3wGenVertexArrays;
extern PFNGLISVERTEXARRAYPROC gl3wIsVertexArray;
extern PFNGLGETUNIFORMINDICESPROC gl3wGetUniformIndices;
extern PFNGLGETACTIVEUNIFORMSIVPROC gl3wGetActiveUniformsiv;
extern PFNGLGETACTIVEUNIFORMNAMEPROC gl3wGetActiveUniformName;
extern PFNGLGETUNIFORMBLOCKINDEXPROC gl3wGetUniformBlockIndex;
extern PFNGLGETACTIVEUNIFORMBLOCKIVPROC gl3wGetActiveUniformBlockiv;
extern PFNGLGETACTIVEUNIFORMBLOCKNAMEPROC gl3wGetActiveUniformBlockName;
extern PFNGLUNIFORMBLOCKBINDINGPROC gl3wUniformBlockBinding;
extern PFNGLCOPYBUFFERSUBDATAPROC gl3wCopyBufferSubData;
extern PFNGLDRAWELEMENTSBASEVERTEXPROC gl3wDrawElementsBaseVertex;
extern PFNGLDRAWRANGEELEMENTSBASEVERTEXPROC gl3wDrawRangeElementsBaseVertex;
extern PFNGLDRAWELEMENTSINSTANCEDBASEVERTEXPROC gl3wDrawElementsInstancedBaseVertex;
extern PFNGLMULTIDRAWELEMENTSBASEVERTEXPROC gl3wMultiDrawElementsBaseVertex;
extern PFNGLPROVOKINGVERTEXPROC gl3wProvokingVertex;
extern PFNGLFENCESYNCPROC gl3wFenceSync;
extern PFNGLISSYNCPROC gl3wIsSync;
extern PFNGLDELETESYNCPROC gl3wDeleteSync;
extern PFNGLCLIENTWAITSYNCPROC gl3wClientWaitSync;
extern PFNGLWAITSYNCPROC gl3wWaitSync;
extern PFNGLGETINTEGER64VPROC gl3wGetInteger64v;
extern PFNGLGETSYNCIVPROC gl3wGetSynciv;
extern PFNGLTEXIMAGE2DMULTISAMPLEPROC gl3wTexImage2DMultisample;
extern PFNGLTEXIMAGE3DMULTISAMPLEPROC gl3wTexImage3DMultisample;
extern PFNGLGETMULTISAMPLEFVPROC gl3wGetMultisamplefv;
extern PFNGLSAMPLEMASKIPROC gl3wSampleMaski;
extern PFNGLBLENDEQUATIONIARBPROC gl3wBlendEquationiARB;
extern PFNGLBLENDEQUATIONSEPARATEIARBPROC gl3wBlendEquationSeparateiARB;
extern PFNGLBLENDFUNCIARBPROC gl3wBlendFunciARB;
extern PFNGLBLENDFUNCSEPARATEIARBPROC gl3wBlendFuncSeparateiARB;
extern PFNGLMINSAMPLESHADINGARBPROC gl3wMinSampleShadingARB;
extern PFNGLNAMEDSTRINGARBPROC gl3wNamedStringARB;
extern PFNGLDELETENAMEDSTRINGARBPROC gl3wDeleteNamedStringARB;
extern PFNGLCOMPILESHADERINCLUDEARBPROC gl3wCompileShaderIncludeARB;
extern PFNGLISNAMEDSTRINGARBPROC gl3wIsNamedStringARB;
extern PFNGLGETNAMEDSTRINGARBPROC gl3wGetNamedStringARB;
extern PFNGLGETNAMEDSTRINGIVARBPROC gl3wGetNamedStringivARB;
extern PFNGLBINDFRAGDATALOCATIONINDEXEDPROC gl3wBindFragDataLocationIndexed;
extern PFNGLGETFRAGDATAINDEXPROC gl3wGetFragDataIndex;
extern PFNGLGENSAMPLERSPROC gl3wGenSamplers;
extern PFNGLDELETESAMPLERSPROC gl3wDeleteSamplers;
extern PFNGLISSAMPLERPROC gl3wIsSampler;
extern PFNGLBINDSAMPLERPROC gl3wBindSampler;
extern PFNGLSAMPLERPARAMETERIPROC gl3wSamplerParameteri;
extern PFNGLSAMPLERPARAMETERIVPROC gl3wSamplerParameteriv;
extern PFNGLSAMPLERPARAMETERFPROC gl3wSamplerParameterf;
extern PFNGLSAMPLERPARAMETERFVPROC gl3wSamplerParameterfv;
extern PFNGLSAMPLERPARAMETERIIVPROC gl3wSamplerParameterIiv;
extern PFNGLSAMPLERPARAMETERIUIVPROC gl3wSamplerParameterIuiv;
extern PFNGLGETSAMPLERPARAMETERIVPROC gl3wGetSamplerParameteriv;
extern PFNGLGETSAMPLERPARAMETERIIVPROC gl3wGetSamplerParameterIiv;
extern PFNGLGETSAMPLERPARAMETERFVPROC gl3wGetSamplerParameterfv;
extern PFNGLGETSAMPLERPARAMETERIUIVPROC gl3wGetSamplerParameterIuiv;
extern PFNGLQUERYCOUNTERPROC gl3wQueryCounter;
extern PFNGLGETQUERYOBJECTI64VPROC gl3wGetQueryObjecti64v;
extern PFNGLGETQUERYOBJECTUI64VPROC gl3wGetQueryObjectui64v;
extern PFNGLVERTEXP2UIPROC gl3wVertexP2ui;
extern PFNGLVERTEXP2UIVPROC gl3wVertexP2uiv;
extern PFNGLVERTEXP3UIPROC gl3wVertexP3ui;
extern PFNGLVERTEXP3UIVPROC gl3wVertexP3uiv;
extern PFNGLVERTEXP4UIPROC gl3wVertexP4ui;
extern PFNGLVERTEXP4UIVPROC gl3wVertexP4uiv;
extern PFNGLTEXCOORDP1UIPROC gl3wTexCoordP1ui;
extern PFNGLTEXCOORDP1UIVPROC gl3wTexCoordP1uiv;
extern PFNGLTEXCOORDP2UIPROC gl3wTexCoordP2ui;
extern PFNGLTEXCOORDP2UIVPROC gl3wTexCoordP2uiv;
extern PFNGLTEXCOORDP3UIPROC gl3wTexCoordP3ui;
extern PFNGLTEXCOORDP3UIVPROC gl3wTexCoordP3uiv;
extern PFNGLTEXCOORDP4UIPROC gl3wTexCoordP4ui;
extern PFNGLTEXCOORDP4UIVPROC gl3wTexCoordP4uiv;
extern PFNGLMULTITEXCOORDP1UIPROC gl3wMultiTexCoordP1ui;
extern PFNGLMULTITEXCOORDP1UIVPROC gl3wMultiTexCoordP1uiv;
extern PFNGLMULTITEXCOORDP2UIPROC gl3wMultiTexCoordP2ui;
extern PFNGLMULTITEXCOORDP2UIVPROC gl3wMultiTexCoordP2uiv;
extern PFNGLMULTITEXCOORDP3UIPROC gl3wMultiTexCoordP3ui;
extern PFNGLMULTITEXCOORDP3UIVPROC gl3wMultiTexCoordP3uiv;
extern PFNGLMULTITEXCOORDP4UIPROC gl3wMultiTexCoordP4ui;
extern PFNGLMULTITEXCOORDP4UIVPROC gl3wMultiTexCoordP4uiv;
extern PFNGLNORMALP3UIPROC gl3wNormalP3ui;
extern PFNGLNORMALP3UIVPROC gl3wNormalP3uiv;
extern PFNGLCOLORP3UIPROC gl3wColorP3ui;
extern PFNGLCOLORP3UIVPROC gl3wColorP3uiv;
extern PFNGLCOLORP4UIPROC gl3wColorP4ui;
extern PFNGLCOLORP4UIVPROC gl3wColorP4uiv;
extern PFNGLSECONDARYCOLORP3UIPROC gl3wSecondaryColorP3ui;
extern PFNGLSECONDARYCOLORP3UIVPROC gl3wSecondaryColorP3uiv;
extern PFNGLVERTEXATTRIBP1UIPROC gl3wVertexAttribP1ui;
extern PFNGLVERTEXATTRIBP1UIVPROC gl3wVertexAttribP1uiv;
extern PFNGLVERTEXATTRIBP2UIPROC gl3wVertexAttribP2ui;
extern PFNGLVERTEXATTRIBP2UIVPROC gl3wVertexAttribP2uiv;
extern PFNGLVERTEXATTRIBP3UIPROC gl3wVertexAttribP3ui;
extern PFNGLVERTEXATTRIBP3UIVPROC gl3wVertexAttribP3uiv;
extern PFNGLVERTEXATTRIBP4UIPROC gl3wVertexAttribP4ui;
extern PFNGLVERTEXATTRIBP4UIVPROC gl3wVertexAttribP4uiv;
extern PFNGLDRAWARRAYSINDIRECTPROC gl3wDrawArraysIndirect;
extern PFNGLDRAWELEMENTSINDIRECTPROC gl3wDrawElementsIndirect;
extern PFNGLUNIFORM1DPROC gl3wUniform1d;
extern PFNGLUNIFORM2DPROC gl3wUniform2d;
extern PFNGLUNIFORM3DPROC gl3wUniform3d;
extern PFNGLUNIFORM4DPROC gl3wUniform4d;
extern PFNGLUNIFORM1DVPROC gl3wUniform1dv;
extern PFNGLUNIFORM2DVPROC gl3wUniform2dv;
extern PFNGLUNIFORM3DVPROC gl3wUniform3dv;
extern PFNGLUNIFORM4DVPROC gl3wUniform4dv;
extern PFNGLUNIFORMMATRIX2DVPROC gl3wUniformMatrix2dv;
extern PFNGLUNIFORMMATRIX3DVPROC gl3wUniformMatrix3dv;
extern PFNGLUNIFORMMATRIX4DVPROC gl3wUniformMatrix4dv;
extern PFNGLUNIFORMMATRIX2X3DVPROC gl3wUniformMatrix2x3dv;
extern PFNGLUNIFORMMATRIX2X4DVPROC gl3wUniformMatrix2x4dv;
extern PFNGLUNIFORMMATRIX3X2DVPROC gl3wUniformMatrix3x2dv;
extern PFNGLUNIFORMMATRIX3X4DVPROC gl3wUniformMatrix3x4dv;
extern PFNGLUNIFORMMATRIX4X2DVPROC gl3wUniformMatrix4x2dv;
extern PFNGLUNIFORMMATRIX4X3DVPROC gl3wUniformMatrix4x3dv;
extern PFNGLGETUNIFORMDVPROC gl3wGetUniformdv;
extern PFNGLGETSUBROUTINEUNIFORMLOCATIONPROC gl3wGetSubroutineUniformLocation;
extern PFNGLGETSUBROUTINEINDEXPROC gl3wGetSubroutineIndex;
extern PFNGLGETACTIVESUBROUTINEUNIFORMIVPROC gl3wGetActiveSubroutineUniformiv;
extern PFNGLGETACTIVESUBROUTINEUNIFORMNAMEPROC gl3wGetActiveSubroutineUniformName;
extern PFNGLGETACTIVESUBROUTINENAMEPROC gl3wGetActiveSubroutineName;
extern PFNGLUNIFORMSUBROUTINESUIVPROC gl3wUniformSubroutinesuiv;
extern PFNGLGETUNIFORMSUBROUTINEUIVPROC gl3wGetUniformSubroutineuiv;
extern PFNGLGETPROGRAMSTAGEIVPROC gl3wGetProgramStageiv;
extern PFNGLPATCHPARAMETERIPROC gl3wPatchParameteri;
extern PFNGLPATCHPARAMETERFVPROC gl3wPatchParameterfv;
extern PFNGLBINDTRANSFORMFEEDBACKPROC gl3wBindTransformFeedback;
extern PFNGLDELETETRANSFORMFEEDBACKSPROC gl3wDeleteTransformFeedbacks;
extern PFNGLGENTRANSFORMFEEDBACKSPROC gl3wGenTransformFeedbacks;
extern PFNGLISTRANSFORMFEEDBACKPROC gl3wIsTransformFeedback;
extern PFNGLPAUSETRANSFORMFEEDBACKPROC gl3wPauseTransformFeedback;
extern PFNGLRESUMETRANSFORMFEEDBACKPROC gl3wResumeTransformFeedback;
extern PFNGLDRAWTRANSFORMFEEDBACKPROC gl3wDrawTransformFeedback;
extern PFNGLDRAWTRANSFORMFEEDBACKSTREAMPROC gl3wDrawTransformFeedbackStream;
extern PFNGLBEGINQUERYINDEXEDPROC gl3wBeginQueryIndexed;
extern PFNGLENDQUERYINDEXEDPROC gl3wEndQueryIndexed;
extern PFNGLGETQUERYINDEXEDIVPROC gl3wGetQueryIndexediv;
extern PFNGLRELEASESHADERCOMPILERPROC gl3wReleaseShaderCompiler;
extern PFNGLSHADERBINARYPROC gl3wShaderBinary;
extern PFNGLGETSHADERPRECISIONFORMATPROC gl3wGetShaderPrecisionFormat;
extern PFNGLDEPTHRANGEFPROC gl3wDepthRangef;
extern PFNGLCLEARDEPTHFPROC gl3wClearDepthf;
extern PFNGLGETPROGRAMBINARYPROC gl3wGetProgramBinary;
extern PFNGLPROGRAMBINARYPROC gl3wProgramBinary;
extern PFNGLPROGRAMPARAMETERIPROC gl3wProgramParameteri;
extern PFNGLUSEPROGRAMSTAGESPROC gl3wUseProgramStages;
extern PFNGLACTIVESHADERPROGRAMPROC gl3wActiveShaderProgram;
extern PFNGLCREATESHADERPROGRAMVPROC gl3wCreateShaderProgramv;
extern PFNGLBINDPROGRAMPIPELINEPROC gl3wBindProgramPipeline;
extern PFNGLDELETEPROGRAMPIPELINESPROC gl3wDeleteProgramPipelines;
extern PFNGLGENPROGRAMPIPELINESPROC gl3wGenProgramPipelines;
extern PFNGLISPROGRAMPIPELINEPROC gl3wIsProgramPipeline;
extern PFNGLGETPROGRAMPIPELINEIVPROC gl3wGetProgramPipelineiv;
extern PFNGLPROGRAMUNIFORM1IPROC gl3wProgramUniform1i;
extern PFNGLPROGRAMUNIFORM1IVPROC gl3wProgramUniform1iv;
extern PFNGLPROGRAMUNIFORM1FPROC gl3wProgramUniform1f;
extern PFNGLPROGRAMUNIFORM1FVPROC gl3wProgramUniform1fv;
extern PFNGLPROGRAMUNIFORM1DPROC gl3wProgramUniform1d;
extern PFNGLPROGRAMUNIFORM1DVPROC gl3wProgramUniform1dv;
extern PFNGLPROGRAMUNIFORM1UIPROC gl3wProgramUniform1ui;
extern PFNGLPROGRAMUNIFORM1UIVPROC gl3wProgramUniform1uiv;
extern PFNGLPROGRAMUNIFORM2IPROC gl3wProgramUniform2i;
extern PFNGLPROGRAMUNIFORM2IVPROC gl3wProgramUniform2iv;
extern PFNGLPROGRAMUNIFORM2FPROC gl3wProgramUniform2f;
extern PFNGLPROGRAMUNIFORM2FVPROC gl3wProgramUniform2fv;
extern PFNGLPROGRAMUNIFORM2DPROC gl3wProgramUniform2d;
extern PFNGLPROGRAMUNIFORM2DVPROC gl3wProgramUniform2dv;
extern PFNGLPROGRAMUNIFORM2UIPROC gl3wProgramUniform2ui;
extern PFNGLPROGRAMUNIFORM2UIVPROC gl3wProgramUniform2uiv;
extern PFNGLPROGRAMUNIFORM3IPROC gl3wProgramUniform3i;
extern PFNGLPROGRAMUNIFORM3IVPROC gl3wProgramUniform3iv;
extern PFNGLPROGRAMUNIFORM3FPROC gl3wProgramUniform3f;
extern PFNGLPROGRAMUNIFORM3FVPROC gl3wProgramUniform3fv;
extern PFNGLPROGRAMUNIFORM3DPROC gl3wProgramUniform3d;
extern PFNGLPROGRAMUNIFORM3DVPROC gl3wProgramUniform3dv;
extern PFNGLPROGRAMUNIFORM3UIPROC gl3wProgramUniform3ui;
extern PFNGLPROGRAMUNIFORM3UIVPROC gl3wProgramUniform3uiv;
extern PFNGLPROGRAMUNIFORM4IPROC gl3wProgramUniform4i;
extern PFNGLPROGRAMUNIFORM4IVPROC gl3wProgramUniform4iv;
extern PFNGLPROGRAMUNIFORM4FPROC gl3wProgramUniform4f;
extern PFNGLPROGRAMUNIFORM4FVPROC gl3wProgramUniform4fv;
extern PFNGLPROGRAMUNIFORM4DPROC gl3wProgramUniform4d;
extern PFNGLPROGRAMUNIFORM4DVPROC gl3wProgramUniform4dv;
extern PFNGLPROGRAMUNIFORM4UIPROC gl3wProgramUniform4ui;
extern PFNGLPROGRAMUNIFORM4UIVPROC gl3wProgramUniform4uiv;
extern PFNGLPROGRAMUNIFORMMATRIX2FVPROC gl3wProgramUniformMatrix2fv;
extern PFNGLPROGRAMUNIFORMMATRIX3FVPROC gl3wProgramUniformMatrix3fv;
extern PFNGLPROGRAMUNIFORMMATRIX4FVPROC gl3wProgramUniformMatrix4fv;
extern PFNGLPROGRAMUNIFORMMATRIX2DVPROC gl3wProgramUniformMatrix2dv;
extern PFNGLPROGRAMUNIFORMMATRIX3DVPROC gl3wProgramUniformMatrix3dv;
extern PFNGLPROGRAMUNIFORMMATRIX4DVPROC gl3wProgramUniformMatrix4dv;
extern PFNGLPROGRAMUNIFORMMATRIX2X3FVPROC gl3wProgramUniformMatrix2x3fv;
extern PFNGLPROGRAMUNIFORMMATRIX3X2FVPROC gl3wProgramUniformMatrix3x2fv;
extern PFNGLPROGRAMUNIFORMMATRIX2X4FVPROC gl3wProgramUniformMatrix2x4fv;
extern PFNGLPROGRAMUNIFORMMATRIX4X2FVPROC gl3wProgramUniformMatrix4x2fv;
extern PFNGLPROGRAMUNIFORMMATRIX3X4FVPROC gl3wProgramUniformMatrix3x4fv;
extern PFNGLPROGRAMUNIFORMMATRIX4X3FVPROC gl3wProgramUniformMatrix4x3fv;
extern PFNGLPROGRAMUNIFORMMATRIX2X3DVPROC gl3wProgramUniformMatrix2x3dv;
extern PFNGLPROGRAMUNIFORMMATRIX3X2DVPROC gl3wProgramUniformMatrix3x2dv;
extern PFNGLPROGRAMUNIFORMMATRIX2X4DVPROC gl3wProgramUniformMatrix2x4dv;
extern PFNGLPROGRAMUNIFORMMATRIX4X2DVPROC gl3wProgramUniformMatrix4x2dv;
extern PFNGLPROGRAMUNIFORMMATRIX3X4DVPROC gl3wProgramUniformMatrix3x4dv;
extern PFNGLPROGRAMUNIFORMMATRIX4X3DVPROC gl3wProgramUniformMatrix4x3dv;
extern PFNGLVALIDATEPROGRAMPIPELINEPROC gl3wValidateProgramPipeline;
extern PFNGLGETPROGRAMPIPELINEINFOLOGPROC gl3wGetProgramPipelineInfoLog;
extern PFNGLVERTEXATTRIBL1DPROC gl3wVertexAttribL1d;
extern PFNGLVERTEXATTRIBL2DPROC gl3wVertexAttribL2d;
extern PFNGLVERTEXATTRIBL3DPROC gl3wVertexAttribL3d;
extern PFNGLVERTEXATTRIBL4DPROC gl3wVertexAttribL4d;
extern PFNGLVERTEXATTRIBL1DVPROC gl3wVertexAttribL1dv;
extern PFNGLVERTEXATTRIBL2DVPROC gl3wVertexAttribL2dv;
extern PFNGLVERTEXATTRIBL3DVPROC gl3wVertexAttribL3dv;
extern PFNGLVERTEXATTRIBL4DVPROC gl3wVertexAttribL4dv;
extern PFNGLVERTEXATTRIBLPOINTERPROC gl3wVertexAttribLPointer;
extern PFNGLGETVERTEXATTRIBLDVPROC gl3wGetVertexAttribLdv;
extern PFNGLVIEWPORTARRAYVPROC gl3wViewportArrayv;
extern PFNGLVIEWPORTINDEXEDFPROC gl3wViewportIndexedf;
extern PFNGLVIEWPORTINDEXEDFVPROC gl3wViewportIndexedfv;
extern PFNGLSCISSORARRAYVPROC gl3wScissorArrayv;
extern PFNGLSCISSORINDEXEDPROC gl3wScissorIndexed;
extern PFNGLSCISSORINDEXEDVPROC gl3wScissorIndexedv;
extern PFNGLDEPTHRANGEARRAYVPROC gl3wDepthRangeArrayv;
extern PFNGLDEPTHRANGEINDEXEDPROC gl3wDepthRangeIndexed;
extern PFNGLGETFLOATI_VPROC gl3wGetFloati_v;
extern PFNGLGETDOUBLEI_VPROC gl3wGetDoublei_v;
extern PFNGLCREATESYNCFROMCLEVENTARBPROC gl3wCreateSyncFromCLeventARB;
extern PFNGLDEBUGMESSAGECONTROLARBPROC gl3wDebugMessageControlARB;
extern PFNGLDEBUGMESSAGEINSERTARBPROC gl3wDebugMessageInsertARB;
extern PFNGLDEBUGMESSAGECALLBACKARBPROC gl3wDebugMessageCallbackARB;
extern PFNGLGETDEBUGMESSAGELOGARBPROC gl3wGetDebugMessageLogARB;
extern PFNGLGETGRAPHICSRESETSTATUSARBPROC gl3wGetGraphicsResetStatusARB;
extern PFNGLGETNTEXIMAGEARBPROC gl3wGetnTexImageARB;
extern PFNGLREADNPIXELSARBPROC gl3wReadnPixelsARB;
extern PFNGLGETNCOMPRESSEDTEXIMAGEARBPROC gl3wGetnCompressedTexImageARB;
extern PFNGLGETNUNIFORMFVARBPROC gl3wGetnUniformfvARB;
extern PFNGLGETNUNIFORMIVARBPROC gl3wGetnUniformivARB;
extern PFNGLGETNUNIFORMUIVARBPROC gl3wGetnUniformuivARB;
extern PFNGLGETNUNIFORMDVARBPROC gl3wGetnUniformdvARB;
extern PFNGLDRAWARRAYSINSTANCEDBASEINSTANCEPROC gl3wDrawArraysInstancedBaseInstance;
extern PFNGLDRAWELEMENTSINSTANCEDBASEINSTANCEPROC gl3wDrawElementsInstancedBaseInstance;
extern PFNGLDRAWELEMENTSINSTANCEDBASEVERTEXBASEINSTANCEPROC gl3wDrawElementsInstancedBaseVertexBaseInstance;
extern PFNGLDRAWTRANSFORMFEEDBACKINSTANCEDPROC gl3wDrawTransformFeedbackInstanced;
extern PFNGLDRAWTRANSFORMFEEDBACKSTREAMINSTANCEDPROC gl3wDrawTransformFeedbackStreamInstanced;
extern PFNGLGETINTERNALFORMATIVPROC gl3wGetInternalformativ;
extern PFNGLGETACTIVEATOMICCOUNTERBUFFERIVPROC gl3wGetActiveAtomicCounterBufferiv;
extern PFNGLBINDIMAGETEXTUREPROC gl3wBindImageTexture;
extern PFNGLMEMORYBARRIERPROC gl3wMemoryBarrier;
extern PFNGLTEXSTORAGE1DPROC gl3wTexStorage1D;
extern PFNGLTEXSTORAGE2DPROC gl3wTexStorage2D;
extern PFNGLTEXSTORAGE3DPROC gl3wTexStorage3D;
extern PFNGLTEXTURESTORAGE1DEXTPROC gl3wTextureStorage1DEXT;
extern PFNGLTEXTURESTORAGE2DEXTPROC gl3wTextureStorage2DEXT;
extern PFNGLTEXTURESTORAGE3DEXTPROC gl3wTextureStorage3DEXT;
extern PFNGLDEBUGMESSAGECONTROLPROC gl3wDebugMessageControl;
extern PFNGLDEBUGMESSAGEINSERTPROC gl3wDebugMessageInsert;
extern PFNGLDEBUGMESSAGECALLBACKPROC gl3wDebugMessageCallback;
extern PFNGLGETDEBUGMESSAGELOGPROC gl3wGetDebugMessageLog;
extern PFNGLPUSHDEBUGGROUPPROC gl3wPushDebugGroup;
extern PFNGLPOPDEBUGGROUPPROC gl3wPopDebugGroup;
extern PFNGLOBJECTLABELPROC gl3wObjectLabel;
extern PFNGLGETOBJECTLABELPROC gl3wGetObjectLabel;
extern PFNGLOBJECTPTRLABELPROC gl3wObjectPtrLabel;
extern PFNGLGETOBJECTPTRLABELPROC gl3wGetObjectPtrLabel;
extern PFNGLCLEARBUFFERDATAPROC gl3wClearBufferData;
extern PFNGLCLEARBUFFERSUBDATAPROC gl3wClearBufferSubData;
extern PFNGLCLEARNAMEDBUFFERDATAEXTPROC gl3wClearNamedBufferDataEXT;
extern PFNGLCLEARNAMEDBUFFERSUBDATAEXTPROC gl3wClearNamedBufferSubDataEXT;
extern PFNGLDISPATCHCOMPUTEPROC gl3wDispatchCompute;
extern PFNGLDISPATCHCOMPUTEINDIRECTPROC gl3wDispatchComputeIndirect;
extern PFNGLCOPYIMAGESUBDATAPROC gl3wCopyImageSubData;
extern PFNGLTEXTUREVIEWPROC gl3wTextureView;
extern PFNGLBINDVERTEXBUFFERPROC gl3wBindVertexBuffer;
extern PFNGLVERTEXATTRIBFORMATPROC gl3wVertexAttribFormat;
extern PFNGLVERTEXATTRIBIFORMATPROC gl3wVertexAttribIFormat;
extern PFNGLVERTEXATTRIBLFORMATPROC gl3wVertexAttribLFormat;
extern PFNGLVERTEXATTRIBBINDINGPROC gl3wVertexAttribBinding;
extern PFNGLVERTEXBINDINGDIVISORPROC gl3wVertexBindingDivisor;
extern PFNGLVERTEXARRAYBINDVERTEXBUFFEREXTPROC gl3wVertexArrayBindVertexBufferEXT;
extern PFNGLVERTEXARRAYVERTEXATTRIBFORMATEXTPROC gl3wVertexArrayVertexAttribFormatEXT;
extern PFNGLVERTEXARRAYVERTEXATTRIBIFORMATEXTPROC gl3wVertexArrayVertexAttribIFormatEXT;
extern PFNGLVERTEXARRAYVERTEXATTRIBLFORMATEXTPROC gl3wVertexArrayVertexAttribLFormatEXT;
extern PFNGLVERTEXARRAYVERTEXATTRIBBINDINGEXTPROC gl3wVertexArrayVertexAttribBindingEXT;
extern PFNGLVERTEXARRAYVERTEXBINDINGDIVISOREXTPROC gl3wVertexArrayVertexBindingDivisorEXT;
extern PFNGLFRAMEBUFFERPARAMETERIPROC gl3wFramebufferParameteri;
extern PFNGLGETFRAMEBUFFERPARAMETERIVPROC gl3wGetFramebufferParameteriv;
extern PFNGLNAMEDFRAMEBUFFERPARAMETERIEXTPROC gl3wNamedFramebufferParameteriEXT;
extern PFNGLGETNAMEDFRAMEBUFFERPARAMETERIVEXTPROC gl3wGetNamedFramebufferParameterivEXT;
extern PFNGLGETINTERNALFORMATI64VPROC gl3wGetInternalformati64v;
extern PFNGLINVALIDATETEXSUBIMAGEPROC gl3wInvalidateTexSubImage;
extern PFNGLINVALIDATETEXIMAGEPROC gl3wInvalidateTexImage;
extern PFNGLINVALIDATEBUFFERSUBDATAPROC gl3wInvalidateBufferSubData;
extern PFNGLINVALIDATEBUFFERDATAPROC gl3wInvalidateBufferData;
extern PFNGLINVALIDATEFRAMEBUFFERPROC gl3wInvalidateFramebuffer;
extern PFNGLINVALIDATESUBFRAMEBUFFERPROC gl3wInvalidateSubFramebuffer;
extern PFNGLMULTIDRAWARRAYSINDIRECTPROC gl3wMultiDrawArraysIndirect;
extern PFNGLMULTIDRAWELEMENTSINDIRECTPROC gl3wMultiDrawElementsIndirect;
extern PFNGLGETPROGRAMINTERFACEIVPROC gl3wGetProgramInterfaceiv;
extern PFNGLGETPROGRAMRESOURCEINDEXPROC gl3wGetProgramResourceIndex;
extern PFNGLGETPROGRAMRESOURCENAMEPROC gl3wGetProgramResourceName;
extern PFNGLGETPROGRAMRESOURCEIVPROC gl3wGetProgramResourceiv;
extern PFNGLGETPROGRAMRESOURCELOCATIONPROC gl3wGetProgramResourceLocation;
extern PFNGLGETPROGRAMRESOURCELOCATIONINDEXPROC gl3wGetProgramResourceLocationIndex;
extern PFNGLSHADERSTORAGEBLOCKBINDINGPROC gl3wShaderStorageBlockBinding;
extern PFNGLTEXBUFFERRANGEPROC gl3wTexBufferRange;
extern PFNGLTEXTUREBUFFERRANGEEXTPROC gl3wTextureBufferRangeEXT;
extern PFNGLTEXSTORAGE2DMULTISAMPLEPROC gl3wTexStorage2DMultisample;
extern PFNGLTEXSTORAGE3DMULTISAMPLEPROC gl3wTexStorage3DMultisample;
extern PFNGLTEXTURESTORAGE2DMULTISAMPLEEXTPROC gl3wTextureStorage2DMultisampleEXT;
extern PFNGLTEXTURESTORAGE3DMULTISAMPLEEXTPROC gl3wTextureStorage3DMultisampleEXT;

#define glCullFace		gl3wCullFace
#define glFrontFace		gl3wFrontFace
#define glHint		gl3wHint
#define glLineWidth		gl3wLineWidth
#define glPointSize		gl3wPointSize
#define glPolygonMode		gl3wPolygonMode
#define glScissor		gl3wScissor
#define glTexParameterf		gl3wTexParameterf
#define glTexParameterfv		gl3wTexParameterfv
#define glTexParameteri		gl3wTexParameteri
#define glTexParameteriv		gl3wTexParameteriv
#define glTexImage1D		gl3wTexImage1D
#define glTexImage2D		gl3wTexImage2D
#define glDrawBuffer		gl3wDrawBuffer
#define glClear		gl3wClear
#define glClearColor		gl3wClearColor
#define glClearStencil		gl3wClearStencil
#define glClearDepth		gl3wClearDepth
#define glStencilMask		gl3wStencilMask
#define glColorMask		gl3wColorMask
#define glDepthMask		gl3wDepthMask
#define glDisable		gl3wDisable
#define glEnable		gl3wEnable
#define glFinish		gl3wFinish
#define glFlush		gl3wFlush
#define glBlendFunc		gl3wBlendFunc
#define glLogicOp		gl3wLogicOp
#define glStencilFunc		gl3wStencilFunc
#define glStencilOp		gl3wStencilOp
#define glDepthFunc		gl3wDepthFunc
#define glPixelStoref		gl3wPixelStoref
#define glPixelStorei		gl3wPixelStorei
#define glReadBuffer		gl3wReadBuffer
#define glReadPixels		gl3wReadPixels
#define glGetBooleanv		gl3wGetBooleanv
#define glGetDoublev		gl3wGetDoublev
#define glGetError		gl3wGetError
#define glGetFloatv		gl3wGetFloatv
#define glGetIntegerv		gl3wGetIntegerv
#define glGetString		gl3wGetString
#define glGetTexImage		gl3wGetTexImage
#define glGetTexParameterfv		gl3wGetTexParameterfv
#define glGetTexParameteriv		gl3wGetTexParameteriv
#define glGetTexLevelParameterfv		gl3wGetTexLevelParameterfv
#define glGetTexLevelParameteriv		gl3wGetTexLevelParameteriv
#define glIsEnabled		gl3wIsEnabled
#define glDepthRange		gl3wDepthRange
#define glViewport		gl3wViewport
#define glDrawArrays		gl3wDrawArrays
#define glDrawElements		gl3wDrawElements
#define glGetPointerv		gl3wGetPointerv
#define glPolygonOffset		gl3wPolygonOffset
#define glCopyTexImage1D		gl3wCopyTexImage1D
#define glCopyTexImage2D		gl3wCopyTexImage2D
#define glCopyTexSubImage1D		gl3wCopyTexSubImage1D
#define glCopyTexSubImage2D		gl3wCopyTexSubImage2D
#define glTexSubImage1D		gl3wTexSubImage1D
#define glTexSubImage2D		gl3wTexSubImage2D
#define glBindTexture		gl3wBindTexture
#define glDeleteTextures		gl3wDeleteTextures
#define glGenTextures		gl3wGenTextures
#define glIsTexture		gl3wIsTexture
#define glBlendColor		gl3wBlendColor
#define glBlendEquation		gl3wBlendEquation
#define glDrawRangeElements		gl3wDrawRangeElements
#define glTexImage3D		gl3wTexImage3D
#define glTexSubImage3D		gl3wTexSubImage3D
#define glCopyTexSubImage3D		gl3wCopyTexSubImage3D
#define glActiveTexture		gl3wActiveTexture
#define glSampleCoverage		gl3wSampleCoverage
#define glCompressedTexImage3D		gl3wCompressedTexImage3D
#define glCompressedTexImage2D		gl3wCompressedTexImage2D
#define glCompressedTexImage1D		gl3wCompressedTexImage1D
#define glCompressedTexSubImage3D		gl3wCompressedTexSubImage3D
#define glCompressedTexSubImage2D		gl3wCompressedTexSubImage2D
#define glCompressedTexSubImage1D		gl3wCompressedTexSubImage1D
#define glGetCompressedTexImage		gl3wGetCompressedTexImage
#define glBlendFuncSeparate		gl3wBlendFuncSeparate
#define glMultiDrawArrays		gl3wMultiDrawArrays
#define glMultiDrawElements		gl3wMultiDrawElements
#define glPointParameterf		gl3wPointParameterf
#define glPointParameterfv		gl3wPointParameterfv
#define glPointParameteri		gl3wPointParameteri
#define glPointParameteriv		gl3wPointParameteriv
#define glGenQueries		gl3wGenQueries
#define glDeleteQueries		gl3wDeleteQueries
#define glIsQuery		gl3wIsQuery
#define glBeginQuery		gl3wBeginQuery
#define glEndQuery		gl3wEndQuery
#define glGetQueryiv		gl3wGetQueryiv
#define glGetQueryObjectiv		gl3wGetQueryObjectiv
#define glGetQueryObjectuiv		gl3wGetQueryObjectuiv
#define glBindBuffer		gl3wBindBuffer
#define glDeleteBuffers		gl3wDeleteBuffers
#define glGenBuffers		gl3wGenBuffers
#define glIsBuffer		gl3wIsBuffer
#define glBufferData		gl3wBufferData
#define glBufferSubData		gl3wBufferSubData
#define glGetBufferSubData		gl3wGetBufferSubData
#define glMapBuffer		gl3wMapBuffer
#define glUnmapBuffer		gl3wUnmapBuffer
#define glGetBufferParameteriv		gl3wGetBufferParameteriv
#define glGetBufferPointerv		gl3wGetBufferPointerv
#define glBlendEquationSeparate		gl3wBlendEquationSeparate
#define glDrawBuffers		gl3wDrawBuffers
#define glStencilOpSeparate		gl3wStencilOpSeparate
#define glStencilFuncSeparate		gl3wStencilFuncSeparate
#define glStencilMaskSeparate		gl3wStencilMaskSeparate
#define glAttachShader		gl3wAttachShader
#define glBindAttribLocation		gl3wBindAttribLocation
#define glCompileShader		gl3wCompileShader
#define glCreateProgram		gl3wCreateProgram
#define glCreateShader		gl3wCreateShader
#define glDeleteProgram		gl3wDeleteProgram
#define glDeleteShader		gl3wDeleteShader
#define glDetachShader		gl3wDetachShader
#define glDisableVertexAttribArray		gl3wDisableVertexAttribArray
#define glEnableVertexAttribArray		gl3wEnableVertexAttribArray
#define glGetActiveAttrib		gl3wGetActiveAttrib
#define glGetActiveUniform		gl3wGetActiveUniform
#define glGetAttachedShaders		gl3wGetAttachedShaders
#define glGetAttribLocation		gl3wGetAttribLocation
#define glGetProgramiv		gl3wGetProgramiv
#define glGetProgramInfoLog		gl3wGetProgramInfoLog
#define glGetShaderiv		gl3wGetShaderiv
#define glGetShaderInfoLog		gl3wGetShaderInfoLog
#define glGetShaderSource		gl3wGetShaderSource
#define glGetUniformLocation		gl3wGetUniformLocation
#define glGetUniformfv		gl3wGetUniformfv
#define glGetUniformiv		gl3wGetUniformiv
#define glGetVertexAttribdv		gl3wGetVertexAttribdv
#define glGetVertexAttribfv		gl3wGetVertexAttribfv
#define glGetVertexAttribiv		gl3wGetVertexAttribiv
#define glGetVertexAttribPointerv		gl3wGetVertexAttribPointerv
#define glIsProgram		gl3wIsProgram
#define glIsShader		gl3wIsShader
#define glLinkProgram		gl3wLinkProgram
#define glShaderSource		gl3wShaderSource
#define glUseProgram		gl3wUseProgram
#define glUniform1f		gl3wUniform1f
#define glUniform2f		gl3wUniform2f
#define glUniform3f		gl3wUniform3f
#define glUniform4f		gl3wUniform4f
#define glUniform1i		gl3wUniform1i
#define glUniform2i		gl3wUniform2i
#define glUniform3i		gl3wUniform3i
#define glUniform4i		gl3wUniform4i
#define glUniform1fv		gl3wUniform1fv
#define glUniform2fv		gl3wUniform2fv
#define glUniform3fv		gl3wUniform3fv
#define glUniform4fv		gl3wUniform4fv
#define glUniform1iv		gl3wUniform1iv
#define glUniform2iv		gl3wUniform2iv
#define glUniform3iv		gl3wUniform3iv
#define glUniform4iv		gl3wUniform4iv
#define glUniformMatrix2fv		gl3wUniformMatrix2fv
#define glUniformMatrix3fv		gl3wUniformMatrix3fv
#define glUniformMatrix4fv		gl3wUniformMatrix4fv
#define glValidateProgram		gl3wValidateProgram
#define glVertexAttrib1d		gl3wVertexAttrib1d
#define glVertexAttrib1dv		gl3wVertexAttrib1dv
#define glVertexAttrib1f		gl3wVertexAttrib1f
#define glVertexAttrib1fv		gl3wVertexAttrib1fv
#define glVertexAttrib1s		gl3wVertexAttrib1s
#define glVertexAttrib1sv		gl3wVertexAttrib1sv
#define glVertexAttrib2d		gl3wVertexAttrib2d
#define glVertexAttrib2dv		gl3wVertexAttrib2dv
#define glVertexAttrib2f		gl3wVertexAttrib2f
#define glVertexAttrib2fv		gl3wVertexAttrib2fv
#define glVertexAttrib2s		gl3wVertexAttrib2s
#define glVertexAttrib2sv		gl3wVertexAttrib2sv
#define glVertexAttrib3d		gl3wVertexAttrib3d
#define glVertexAttrib3dv		gl3wVertexAttrib3dv
#define glVertexAttrib3f		gl3wVertexAttrib3f
#define glVertexAttrib3fv		gl3wVertexAttrib3fv
#define glVertexAttrib3s		gl3wVertexAttrib3s
#define glVertexAttrib3sv		gl3wVertexAttrib3sv
#define glVertexAttrib4Nbv		gl3wVertexAttrib4Nbv
#define glVertexAttrib4Niv		gl3wVertexAttrib4Niv
#define glVertexAttrib4Nsv		gl3wVertexAttrib4Nsv
#define glVertexAttrib4Nub		gl3wVertexAttrib4Nub
#define glVertexAttrib4Nubv		gl3wVertexAttrib4Nubv
#define glVertexAttrib4Nuiv		gl3wVertexAttrib4Nuiv
#define glVertexAttrib4Nusv		gl3wVertexAttrib4Nusv
#define glVertexAttrib4bv		gl3wVertexAttrib4bv
#define glVertexAttrib4d		gl3wVertexAttrib4d
#define glVertexAttrib4dv		gl3wVertexAttrib4dv
#define glVertexAttrib4f		gl3wVertexAttrib4f
#define glVertexAttrib4fv		gl3wVertexAttrib4fv
#define glVertexAttrib4iv		gl3wVertexAttrib4iv
#define glVertexAttrib4s		gl3wVertexAttrib4s
#define glVertexAttrib4sv		gl3wVertexAttrib4sv
#define glVertexAttrib4ubv		gl3wVertexAttrib4ubv
#define glVertexAttrib4uiv		gl3wVertexAttrib4uiv
#define glVertexAttrib4usv		gl3wVertexAttrib4usv
#define glVertexAttribPointer		gl3wVertexAttribPointer
#define glUniformMatrix2x3fv		gl3wUniformMatrix2x3fv
#define glUniformMatrix3x2fv		gl3wUniformMatrix3x2fv
#define glUniformMatrix2x4fv		gl3wUniformMatrix2x4fv
#define glUniformMatrix4x2fv		gl3wUniformMatrix4x2fv
#define glUniformMatrix3x4fv		gl3wUniformMatrix3x4fv
#define glUniformMatrix4x3fv		gl3wUniformMatrix4x3fv
#define glColorMaski		gl3wColorMaski
#define glGetBooleani_v		gl3wGetBooleani_v
#define glGetIntegeri_v		gl3wGetIntegeri_v
#define glEnablei		gl3wEnablei
#define glDisablei		gl3wDisablei
#define glIsEnabledi		gl3wIsEnabledi
#define glBeginTransformFeedback		gl3wBeginTransformFeedback
#define glEndTransformFeedback		gl3wEndTransformFeedback
#define glBindBufferRange		gl3wBindBufferRange
#define glBindBufferBase		gl3wBindBufferBase
#define glTransformFeedbackVaryings		gl3wTransformFeedbackVaryings
#define glGetTransformFeedbackVarying		gl3wGetTransformFeedbackVarying
#define glClampColor		gl3wClampColor
#define glBeginConditionalRender		gl3wBeginConditionalRender
#define glEndConditionalRender		gl3wEndConditionalRender
#define glVertexAttribIPointer		gl3wVertexAttribIPointer
#define glGetVertexAttribIiv		gl3wGetVertexAttribIiv
#define glGetVertexAttribIuiv		gl3wGetVertexAttribIuiv
#define glVertexAttribI1i		gl3wVertexAttribI1i
#define glVertexAttribI2i		gl3wVertexAttribI2i
#define glVertexAttribI3i		gl3wVertexAttribI3i
#define glVertexAttribI4i		gl3wVertexAttribI4i
#define glVertexAttribI1ui		gl3wVertexAttribI1ui
#define glVertexAttribI2ui		gl3wVertexAttribI2ui
#define glVertexAttribI3ui		gl3wVertexAttribI3ui
#define glVertexAttribI4ui		gl3wVertexAttribI4ui
#define glVertexAttribI1iv		gl3wVertexAttribI1iv
#define glVertexAttribI2iv		gl3wVertexAttribI2iv
#define glVertexAttribI3iv		gl3wVertexAttribI3iv
#define glVertexAttribI4iv		gl3wVertexAttribI4iv
#define glVertexAttribI1uiv		gl3wVertexAttribI1uiv
#define glVertexAttribI2uiv		gl3wVertexAttribI2uiv
#define glVertexAttribI3uiv		gl3wVertexAttribI3uiv
#define glVertexAttribI4uiv		gl3wVertexAttribI4uiv
#define glVertexAttribI4bv		gl3wVertexAttribI4bv
#define glVertexAttribI4sv		gl3wVertexAttribI4sv
#define glVertexAttribI4ubv		gl3wVertexAttribI4ubv
#define glVertexAttribI4usv		gl3wVertexAttribI4usv
#define glGetUniformuiv		gl3wGetUniformuiv
#define glBindFragDataLocation		gl3wBindFragDataLocation
#define glGetFragDataLocation		gl3wGetFragDataLocation
#define glUniform1ui		gl3wUniform1ui
#define glUniform2ui		gl3wUniform2ui
#define glUniform3ui		gl3wUniform3ui
#define glUniform4ui		gl3wUniform4ui
#define glUniform1uiv		gl3wUniform1uiv
#define glUniform2uiv		gl3wUniform2uiv
#define glUniform3uiv		gl3wUniform3uiv
#define glUniform4uiv		gl3wUniform4uiv
#define glTexParameterIiv		gl3wTexParameterIiv
#define glTexParameterIuiv		gl3wTexParameterIuiv
#define glGetTexParameterIiv		gl3wGetTexParameterIiv
#define glGetTexParameterIuiv		gl3wGetTexParameterIuiv
#define glClearBufferiv		gl3wClearBufferiv
#define glClearBufferuiv		gl3wClearBufferuiv
#define glClearBufferfv		gl3wClearBufferfv
#define glClearBufferfi		gl3wClearBufferfi
#define glGetStringi		gl3wGetStringi
#define glDrawArraysInstanced		gl3wDrawArraysInstanced
#define glDrawElementsInstanced		gl3wDrawElementsInstanced
#define glTexBuffer		gl3wTexBuffer
#define glPrimitiveRestartIndex		gl3wPrimitiveRestartIndex
#define glGetInteger64i_v		gl3wGetInteger64i_v
#define glGetBufferParameteri64v		gl3wGetBufferParameteri64v
#define glFramebufferTexture		gl3wFramebufferTexture
#define glVertexAttribDivisor		gl3wVertexAttribDivisor
#define glMinSampleShading		gl3wMinSampleShading
#define glBlendEquationi		gl3wBlendEquationi
#define glBlendEquationSeparatei		gl3wBlendEquationSeparatei
#define glBlendFunci		gl3wBlendFunci
#define glBlendFuncSeparatei		gl3wBlendFuncSeparatei
#define glIsRenderbuffer		gl3wIsRenderbuffer
#define glBindRenderbuffer		gl3wBindRenderbuffer
#define glDeleteRenderbuffers		gl3wDeleteRenderbuffers
#define glGenRenderbuffers		gl3wGenRenderbuffers
#define glRenderbufferStorage		gl3wRenderbufferStorage
#define glGetRenderbufferParameteriv		gl3wGetRenderbufferParameteriv
#define glIsFramebuffer		gl3wIsFramebuffer
#define glBindFramebuffer		gl3wBindFramebuffer
#define glDeleteFramebuffers		gl3wDeleteFramebuffers
#define glGenFramebuffers		gl3wGenFramebuffers
#define glCheckFramebufferStatus		gl3wCheckFramebufferStatus
#define glFramebufferTexture1D		gl3wFramebufferTexture1D
#define glFramebufferTexture2D		gl3wFramebufferTexture2D
#define glFramebufferTexture3D		gl3wFramebufferTexture3D
#define glFramebufferRenderbuffer		gl3wFramebufferRenderbuffer
#define glGetFramebufferAttachmentParameteriv		gl3wGetFramebufferAttachmentParameteriv
#define glGenerateMipmap		gl3wGenerateMipmap
#define glBlitFramebuffer		gl3wBlitFramebuffer
#define glRenderbufferStorageMultisample		gl3wRenderbufferStorageMultisample
#define glFramebufferTextureLayer		gl3wFramebufferTextureLayer
#define glMapBufferRange		gl3wMapBufferRange
#define glFlushMappedBufferRange		gl3wFlushMappedBufferRange
#define glBindVertexArray		gl3wBindVertexArray
#define glDeleteVertexArrays		gl3wDeleteVertexArrays
#define glGenVertexArrays		gl3wGenVertexArrays
#define glIsVertexArray		gl3wIsVertexArray
#define glGetUniformIndices		gl3wGetUniformIndices
#define glGetActiveUniformsiv		gl3wGetActiveUniformsiv
#define glGetActiveUniformName		gl3wGetActiveUniformName
#define glGetUniformBlockIndex		gl3wGetUniformBlockIndex
#define glGetActiveUniformBlockiv		gl3wGetActiveUniformBlockiv
#define glGetActiveUniformBlockName		gl3wGetActiveUniformBlockName
#define glUniformBlockBinding		gl3wUniformBlockBinding
#define glCopyBufferSubData		gl3wCopyBufferSubData
#define glDrawElementsBaseVertex		gl3wDrawElementsBaseVertex
#define glDrawRangeElementsBaseVertex		gl3wDrawRangeElementsBaseVertex
#define glDrawElementsInstancedBaseVertex		gl3wDrawElementsInstancedBaseVertex
#define glMultiDrawElementsBaseVertex		gl3wMultiDrawElementsBaseVertex
#define glProvokingVertex		gl3wProvokingVertex
#define glFenceSync		gl3wFenceSync
#define glIsSync		gl3wIsSync
#define glDeleteSync		gl3wDeleteSync
#define glClientWaitSync		gl3wClientWaitSync
#define glWaitSync		gl3wWaitSync
#define glGetInteger64v		gl3wGetInteger64v
#define glGetSynciv		gl3wGetSynciv
#define glTexImage2DMultisample		gl3wTexImage2DMultisample
#define glTexImage3DMultisample		gl3wTexImage3DMultisample
#define glGetMultisamplefv		gl3wGetMultisamplefv
#define glSampleMaski		gl3wSampleMaski
#define glBlendEquationiARB		gl3wBlendEquationiARB
#define glBlendEquationSeparateiARB		gl3wBlendEquationSeparateiARB
#define glBlendFunciARB		gl3wBlendFunciARB
#define glBlendFuncSeparateiARB		gl3wBlendFuncSeparateiARB
#define glMinSampleShadingARB		gl3wMinSampleShadingARB
#define glNamedStringARB		gl3wNamedStringARB
#define glDeleteNamedStringARB		gl3wDeleteNamedStringARB
#define glCompileShaderIncludeARB		gl3wCompileShaderIncludeARB
#define glIsNamedStringARB		gl3wIsNamedStringARB
#define glGetNamedStringARB		gl3wGetNamedStringARB
#define glGetNamedStringivARB		gl3wGetNamedStringivARB
#define glBindFragDataLocationIndexed		gl3wBindFragDataLocationIndexed
#define glGetFragDataIndex		gl3wGetFragDataIndex
#define glGenSamplers		gl3wGenSamplers
#define glDeleteSamplers		gl3wDeleteSamplers
#define glIsSampler		gl3wIsSampler
#define glBindSampler		gl3wBindSampler
#define glSamplerParameteri		gl3wSamplerParameteri
#define glSamplerParameteriv		gl3wSamplerParameteriv
#define glSamplerParameterf		gl3wSamplerParameterf
#define glSamplerParameterfv		gl3wSamplerParameterfv
#define glSamplerParameterIiv		gl3wSamplerParameterIiv
#define glSamplerParameterIuiv		gl3wSamplerParameterIuiv
#define glGetSamplerParameteriv		gl3wGetSamplerParameteriv
#define glGetSamplerParameterIiv		gl3wGetSamplerParameterIiv
#define glGetSamplerParameterfv		gl3wGetSamplerParameterfv
#define glGetSamplerParameterIuiv		gl3wGetSamplerParameterIuiv
#define glQueryCounter		gl3wQueryCounter
#define glGetQueryObjecti64v		gl3wGetQueryObjecti64v
#define glGetQueryObjectui64v		gl3wGetQueryObjectui64v
#define glVertexP2ui		gl3wVertexP2ui
#define glVertexP2uiv		gl3wVertexP2uiv
#define glVertexP3ui		gl3wVertexP3ui
#define glVertexP3uiv		gl3wVertexP3uiv
#define glVertexP4ui		gl3wVertexP4ui
#define glVertexP4uiv		gl3wVertexP4uiv
#define glTexCoordP1ui		gl3wTexCoordP1ui
#define glTexCoordP1uiv		gl3wTexCoordP1uiv
#define glTexCoordP2ui		gl3wTexCoordP2ui
#define glTexCoordP2uiv		gl3wTexCoordP2uiv
#define glTexCoordP3ui		gl3wTexCoordP3ui
#define glTexCoordP3uiv		gl3wTexCoordP3uiv
#define glTexCoordP4ui		gl3wTexCoordP4ui
#define glTexCoordP4uiv		gl3wTexCoordP4uiv
#define glMultiTexCoordP1ui		gl3wMultiTexCoordP1ui
#define glMultiTexCoordP1uiv		gl3wMultiTexCoordP1uiv
#define glMultiTexCoordP2ui		gl3wMultiTexCoordP2ui
#define glMultiTexCoordP2uiv		gl3wMultiTexCoordP2uiv
#define glMultiTexCoordP3ui		gl3wMultiTexCoordP3ui
#define glMultiTexCoordP3uiv		gl3wMultiTexCoordP3uiv
#define glMultiTexCoordP4ui		gl3wMultiTexCoordP4ui
#define glMultiTexCoordP4uiv		gl3wMultiTexCoordP4uiv
#define glNormalP3ui		gl3wNormalP3ui
#define glNormalP3uiv		gl3wNormalP3uiv
#define glColorP3ui		gl3wColorP3ui
#define glColorP3uiv		gl3wColorP3uiv
#define glColorP4ui		gl3wColorP4ui
#define glColorP4uiv		gl3wColorP4uiv
#define glSecondaryColorP3ui		gl3wSecondaryColorP3ui
#define glSecondaryColorP3uiv		gl3wSecondaryColorP3uiv
#define glVertexAttribP1ui		gl3wVertexAttribP1ui
#define glVertexAttribP1uiv		gl3wVertexAttribP1uiv
#define glVertexAttribP2ui		gl3wVertexAttribP2ui
#define glVertexAttribP2uiv		gl3wVertexAttribP2uiv
#define glVertexAttribP3ui		gl3wVertexAttribP3ui
#define glVertexAttribP3uiv		gl3wVertexAttribP3uiv
#define glVertexAttribP4ui		gl3wVertexAttribP4ui
#define glVertexAttribP4uiv		gl3wVertexAttribP4uiv
#define glDrawArraysIndirect		gl3wDrawArraysIndirect
#define glDrawElementsIndirect		gl3wDrawElementsIndirect
#define glUniform1d		gl3wUniform1d
#define glUniform2d		gl3wUniform2d
#define glUniform3d		gl3wUniform3d
#define glUniform4d		gl3wUniform4d
#define glUniform1dv		gl3wUniform1dv
#define glUniform2dv		gl3wUniform2dv
#define glUniform3dv		gl3wUniform3dv
#define glUniform4dv		gl3wUniform4dv
#define glUniformMatrix2dv		gl3wUniformMatrix2dv
#define glUniformMatrix3dv		gl3wUniformMatrix3dv
#define glUniformMatrix4dv		gl3wUniformMatrix4dv
#define glUniformMatrix2x3dv		gl3wUniformMatrix2x3dv
#define glUniformMatrix2x4dv		gl3wUniformMatrix2x4dv
#define glUniformMatrix3x2dv		gl3wUniformMatrix3x2dv
#define glUniformMatrix3x4dv		gl3wUniformMatrix3x4dv
#define glUniformMatrix4x2dv		gl3wUniformMatrix4x2dv
#define glUniformMatrix4x3dv		gl3wUniformMatrix4x3dv
#define glGetUniformdv		gl3wGetUniformdv
#define glGetSubroutineUniformLocation		gl3wGetSubroutineUniformLocation
#define glGetSubroutineIndex		gl3wGetSubroutineIndex
#define glGetActiveSubroutineUniformiv		gl3wGetActiveSubroutineUniformiv
#define glGetActiveSubroutineUniformName		gl3wGetActiveSubroutineUniformName
#define glGetActiveSubroutineName		gl3wGetActiveSubroutineName
#define glUniformSubroutinesuiv		gl3wUniformSubroutinesuiv
#define glGetUniformSubroutineuiv		gl3wGetUniformSubroutineuiv
#define glGetProgramStageiv		gl3wGetProgramStageiv
#define glPatchParameteri		gl3wPatchParameteri
#define glPatchParameterfv		gl3wPatchParameterfv
#define glBindTransformFeedback		gl3wBindTransformFeedback
#define glDeleteTransformFeedbacks		gl3wDeleteTransformFeedbacks
#define glGenTransformFeedbacks		gl3wGenTransformFeedbacks
#define glIsTransformFeedback		gl3wIsTransformFeedback
#define glPauseTransformFeedback		gl3wPauseTransformFeedback
#define glResumeTransformFeedback		gl3wResumeTransformFeedback
#define glDrawTransformFeedback		gl3wDrawTransformFeedback
#define glDrawTransformFeedbackStream		gl3wDrawTransformFeedbackStream
#define glBeginQueryIndexed		gl3wBeginQueryIndexed
#define glEndQueryIndexed		gl3wEndQueryIndexed
#define glGetQueryIndexediv		gl3wGetQueryIndexediv
#define glReleaseShaderCompiler		gl3wReleaseShaderCompiler
#define glShaderBinary		gl3wShaderBinary
#define glGetShaderPrecisionFormat		gl3wGetShaderPrecisionFormat
#define glDepthRangef		gl3wDepthRangef
#define glClearDepthf		gl3wClearDepthf
#define glGetProgramBinary		gl3wGetProgramBinary
#define glProgramBinary		gl3wProgramBinary
#define glProgramParameteri		gl3wProgramParameteri
#define glUseProgramStages		gl3wUseProgramStages
#define glActiveShaderProgram		gl3wActiveShaderProgram
#define glCreateShaderProgramv		gl3wCreateShaderProgramv
#define glBindProgramPipeline		gl3wBindProgramPipeline
#define glDeleteProgramPipelines		gl3wDeleteProgramPipelines
#define glGenProgramPipelines		gl3wGenProgramPipelines
#define glIsProgramPipeline		gl3wIsProgramPipeline
#define glGetProgramPipelineiv		gl3wGetProgramPipelineiv
#define glProgramUniform1i		gl3wProgramUniform1i
#define glProgramUniform1iv		gl3wProgramUniform1iv
#define glProgramUniform1f		gl3wProgramUniform1f
#define glProgramUniform1fv		gl3wProgramUniform1fv
#define glProgramUniform1d		gl3wProgramUniform1d
#define glProgramUniform1dv		gl3wProgramUniform1dv
#define glProgramUniform1ui		gl3wProgramUniform1ui
#define glProgramUniform1uiv		gl3wProgramUniform1uiv
#define glProgramUniform2i		gl3wProgramUniform2i
#define glProgramUniform2iv		gl3wProgramUniform2iv
#define glProgramUniform2f		gl3wProgramUniform2f
#define glProgramUniform2fv		gl3wProgramUniform2fv
#define glProgramUniform2d		gl3wProgramUniform2d
#define glProgramUniform2dv		gl3wProgramUniform2dv
#define glProgramUniform2ui		gl3wProgramUniform2ui
#define glProgramUniform2uiv		gl3wProgramUniform2uiv
#define glProgramUniform3i		gl3wProgramUniform3i
#define glProgramUniform3iv		gl3wProgramUniform3iv
#define glProgramUniform3f		gl3wProgramUniform3f
#define glProgramUniform3fv		gl3wProgramUniform3fv
#define glProgramUniform3d		gl3wProgramUniform3d
#define glProgramUniform3dv		gl3wProgramUniform3dv
#define glProgramUniform3ui		gl3wProgramUniform3ui
#define glProgramUniform3uiv		gl3wProgramUniform3uiv
#define glProgramUniform4i		gl3wProgramUniform4i
#define glProgramUniform4iv		gl3wProgramUniform4iv
#define glProgramUniform4f		gl3wProgramUniform4f
#define glProgramUniform4fv		gl3wProgramUniform4fv
#define glProgramUniform4d		gl3wProgramUniform4d
#define glProgramUniform4dv		gl3wProgramUniform4dv
#define glProgramUniform4ui		gl3wProgramUniform4ui
#define glProgramUniform4uiv		gl3wProgramUniform4uiv
#define glProgramUniformMatrix2fv		gl3wProgramUniformMatrix2fv
#define glProgramUniformMatrix3fv		gl3wProgramUniformMatrix3fv
#define glProgramUniformMatrix4fv		gl3wProgramUniformMatrix4fv
#define glProgramUniformMatrix2dv		gl3wProgramUniformMatrix2dv
#define glProgramUniformMatrix3dv		gl3wProgramUniformMatrix3dv
#define glProgramUniformMatrix4dv		gl3wProgramUniformMatrix4dv
#define glProgramUniformMatrix2x3fv		gl3wProgramUniformMatrix2x3fv
#define glProgramUniformMatrix3x2fv		gl3wProgramUniformMatrix3x2fv
#define glProgramUniformMatrix2x4fv		gl3wProgramUniformMatrix2x4fv
#define glProgramUniformMatrix4x2fv		gl3wProgramUniformMatrix4x2fv
#define glProgramUniformMatrix3x4fv		gl3wProgramUniformMatrix3x4fv
#define glProgramUniformMatrix4x3fv		gl3wProgramUniformMatrix4x3fv
#define glProgramUniformMatrix2x3dv		gl3wProgramUniformMatrix2x3dv
#define glProgramUniformMatrix3x2dv		gl3wProgramUniformMatrix3x2dv
#define glProgramUniformMatrix2x4dv		gl3wProgramUniformMatrix2x4dv
#define glProgramUniformMatrix4x2dv		gl3wProgramUniformMatrix4x2dv
#define glProgramUniformMatrix3x4dv		gl3wProgramUniformMatrix3x4dv
#define glProgramUniformMatrix4x3dv		gl3wProgramUniformMatrix4x3dv
#define glValidateProgramPipeline		gl3wValidateProgramPipeline
#define glGetProgramPipelineInfoLog		gl3wGetProgramPipelineInfoLog
#define glVertexAttribL1d		gl3wVertexAttribL1d
#define glVertexAttribL2d		gl3wVertexAttribL2d
#define glVertexAttribL3d		gl3wVertexAttribL3d
#define glVertexAttribL4d		gl3wVertexAttribL4d
#define glVertexAttribL1dv		gl3wVertexAttribL1dv
#define glVertexAttribL2dv		gl3wVertexAttribL2dv
#define glVertexAttribL3dv		gl3wVertexAttribL3dv
#define glVertexAttribL4dv		gl3wVertexAttribL4dv
#define glVertexAttribLPointer		gl3wVertexAttribLPointer
#define glGetVertexAttribLdv		gl3wGetVertexAttribLdv
#define glViewportArrayv		gl3wViewportArrayv
#define glViewportIndexedf		gl3wViewportIndexedf
#define glViewportIndexedfv		gl3wViewportIndexedfv
#define glScissorArrayv		gl3wScissorArrayv
#define glScissorIndexed		gl3wScissorIndexed
#define glScissorIndexedv		gl3wScissorIndexedv
#define glDepthRangeArrayv		gl3wDepthRangeArrayv
#define glDepthRangeIndexed		gl3wDepthRangeIndexed
#define glGetFloati_v		gl3wGetFloati_v
#define glGetDoublei_v		gl3wGetDoublei_v
#define glCreateSyncFromCLeventARB		gl3wCreateSyncFromCLeventARB
#define glDebugMessageControlARB		gl3wDebugMessageControlARB
#define glDebugMessageInsertARB		gl3wDebugMessageInsertARB
#define glDebugMessageCallbackARB		gl3wDebugMessageCallbackARB
#define glGetDebugMessageLogARB		gl3wGetDebugMessageLogARB
#define glGetGraphicsResetStatusARB		gl3wGetGraphicsResetStatusARB
#define glGetnTexImageARB		gl3wGetnTexImageARB
#define glReadnPixelsARB		gl3wReadnPixelsARB
#define glGetnCompressedTexImageARB		gl3wGetnCompressedTexImageARB
#define glGetnUniformfvARB		gl3wGetnUniformfvARB
#define glGetnUniformivARB		gl3wGetnUniformivARB
#define glGetnUniformuivARB		gl3wGetnUniformuivARB
#define glGetnUniformdvARB		gl3wGetnUniformdvARB
#define glDrawArraysInstancedBaseInstance		gl3wDrawArraysInstancedBaseInstance
#define glDrawElementsInstancedBaseInstance		gl3wDrawElementsInstancedBaseInstance
#define glDrawElementsInstancedBaseVertexBaseInstance		gl3wDrawElementsInstancedBaseVertexBaseInstance
#define glDrawTransformFeedbackInstanced		gl3wDrawTransformFeedbackInstanced
#define glDrawTransformFeedbackStreamInstanced		gl3wDrawTransformFeedbackStreamInstanced
#define glGetInternalformativ		gl3wGetInternalformativ
#define glGetActiveAtomicCounterBufferiv		gl3wGetActiveAtomicCounterBufferiv
#define glBindImageTexture		gl3wBindImageTexture
#define glMemoryBarrier		gl3wMemoryBarrier
#define glTexStorage1D		gl3wTexStorage1D
#define glTexStorage2D		gl3wTexStorage2D
#define glTexStorage3D		gl3wTexStorage3D
#define glTextureStorage1DEXT		gl3wTextureStorage1DEXT
#define glTextureStorage2DEXT		gl3wTextureStorage2DEXT
#define glTextureStorage3DEXT		gl3wTextureStorage3DEXT
#define glDebugMessageControl		gl3wDebugMessageControl
#define glDebugMessageInsert		gl3wDebugMessageInsert
#define glDebugMessageCallback		gl3wDebugMessageCallback
#define glGetDebugMessageLog		gl3wGetDebugMessageLog
#define glPushDebugGroup		gl3wPushDebugGroup
#define glPopDebugGroup		gl3wPopDebugGroup
#define glObjectLabel		gl3wObjectLabel
#define glGetObjectLabel		gl3wGetObjectLabel
#define glObjectPtrLabel		gl3wObjectPtrLabel
#define glGetObjectPtrLabel		gl3wGetObjectPtrLabel
#define glClearBufferData		gl3wClearBufferData
#define glClearBufferSubData		gl3wClearBufferSubData
#define glClearNamedBufferDataEXT		gl3wClearNamedBufferDataEXT
#define glClearNamedBufferSubDataEXT		gl3wClearNamedBufferSubDataEXT
#define glDispatchCompute		gl3wDispatchCompute
#define glDispatchComputeIndirect		gl3wDispatchComputeIndirect
#define glCopyImageSubData		gl3wCopyImageSubData
#define glTextureView		gl3wTextureView
#define glBindVertexBuffer		gl3wBindVertexBuffer
#define glVertexAttribFormat		gl3wVertexAttribFormat
#define glVertexAttribIFormat		gl3wVertexAttribIFormat
#define glVertexAttribLFormat		gl3wVertexAttribLFormat
#define glVertexAttribBinding		gl3wVertexAttribBinding
#define glVertexBindingDivisor		gl3wVertexBindingDivisor
#define glVertexArrayBindVertexBufferEXT		gl3wVertexArrayBindVertexBufferEXT
#define glVertexArrayVertexAttribFormatEXT		gl3wVertexArrayVertexAttribFormatEXT
#define glVertexArrayVertexAttribIFormatEXT		gl3wVertexArrayVertexAttribIFormatEXT
#define glVertexArrayVertexAttribLFormatEXT		gl3wVertexArrayVertexAttribLFormatEXT
#define glVertexArrayVertexAttribBindingEXT		gl3wVertexArrayVertexAttribBindingEXT
#define glVertexArrayVertexBindingDivisorEXT		gl3wVertexArrayVertexBindingDivisorEXT
#define glFramebufferParameteri		gl3wFramebufferParameteri
#define glGetFramebufferParameteriv		gl3wGetFramebufferParameteriv
#define glNamedFramebufferParameteriEXT		gl3wNamedFramebufferParameteriEXT
#define glGetNamedFramebufferParameterivEXT		gl3wGetNamedFramebufferParameterivEXT
#define glGetInternalformati64v		gl3wGetInternalformati64v
#define glInvalidateTexSubImage		gl3wInvalidateTexSubImage
#define glInvalidateTexImage		gl3wInvalidateTexImage
#define glInvalidateBufferSubData		gl3wInvalidateBufferSubData
#define glInvalidateBufferData		gl3wInvalidateBufferData
#define glInvalidateFramebuffer		gl3wInvalidateFramebuffer
#define glInvalidateSubFramebuffer		gl3wInvalidateSubFramebuffer
#define glMultiDrawArraysIndirect		gl3wMultiDrawArraysIndirect
#define glMultiDrawElementsIndirect		gl3wMultiDrawElementsIndirect
#define glGetProgramInterfaceiv		gl3wGetProgramInterfaceiv
#define glGetProgramResourceIndex		gl3wGetProgramResourceIndex
#define glGetProgramResourceName		gl3wGetProgramResourceName
#define glGetProgramResourceiv		gl3wGetProgramResourceiv
#define glGetProgramResourceLocation		gl3wGetProgramResourceLocation
#define glGetProgramResourceLocationIndex		gl3wGetProgramResourceLocationIndex
#define glShaderStorageBlockBinding		gl3wShaderStorageBlockBinding
#define glTexBufferRange		gl3wTexBufferRange
#define glTextureBufferRangeEXT		gl3wTextureBufferRangeEXT
#define glTexStorage2DMultisample		gl3wTexStorage2DMultisample
#define glTexStorage3DMultisample		gl3wTexStorage3DMultisample
#define glTextureStorage2DMultisampleEXT		gl3wTextureStorage2DMultisampleEXT
#define glTextureStorage3DMultisampleEXT		gl3wTextureStorage3DMultisampleEXT

#ifdef __cplusplus
}
#endif

#endif
